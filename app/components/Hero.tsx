import {
  TrendingUp,
  Zap,
} from "lucide-react";
import React from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { FeatureCard } from "@/components/ui/feature-card";
import { Container, Section } from "@/components/ui/section";

  return (
    <Section variant="hero" background="black" overflow="hidd
      <div className="absolute inset-0">
        <div className="absolute inset-0 opaci from-blue-500/10 via-purple-500/10 to-cyan-500/10 animate-pulse" />
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `
                linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px),
                linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px)
              `,
              backgroundSize: "40px 40px",
              animation: "matrix-scroll 20s linear infinite",
            }}
          />
        </div>

        {/* Floating Orbs with Complex Animations */}
        <div className="absolute top-20 left-20 w-32 h-32 bg-gradient-to-r from-blue-500/30 to-cyan-500/30 rounded-full blur-xl animate-float-complex opacity-60" />
        <div className="absolute top-40 right-32 w-24 h-24 bg-gradient-to-r from-purple-500/40 to-pink-500/40 rounded-full blur-xl animate-float-complex delay-1000 opacity-50" />
        <div className="absolute bottom-32 left-40 w-40 h-40 bg-gradient-to-r from-amber-500/20 to-orange-500/20 rounded-full blur-2xl animate-float-complex delay-2000 opacity-40" />
        <div className="absolute top-60 right-20 w-16 h-16 bg-gradient-to-r from-green-500/50 to-emerald-500/50 rounded-full blur-lg animate-float-complex delay-3000 opacity-70" />

        {/* Animated Particles */}
        {[...Array(12)].map((_, i) => (
          <div
            key={i}
            className={"absolute w-1 h-1 bg-blue-400 rounded-full animate-particle-drift opacity-60"}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${i * 0.5}s`,
              animationDuration: `${3 + Math.random() * 4}s`,
            }}
          />
        ))}

        {/* Dynamic Gradient Waves */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-500/50 to-transparent animate-wave-slide" />
          <div className="absolute top-1/2 left-0 w-full h-px bg-gradient-to-r from-transparent via-purple-500/40 to-transparent animate-wave-slide delay-1000" />
          <div className="absolute bottom-1/3 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyan-500/30 to-transparent animate-wave-slide delay-2000" />
        </div>

        {/* Rotating Geometric Shapes */}
        <div className="absolute top-1/3 right-1/4 w-20 h-20 border border-blue-500/20 rotate-45 animate-spin-slow" />
        <div className="absolute bottom-1/3 left-1/4 w-16 h-16 border border-purple-500/20 animate-spin-reverse" />
        <div className="absolute top-2/3 right-1/3 w-12 h-12 border border-cyan-500/20 rotate-12 animate-spin-slow delay-1000" />
      </div>

      {/* Interactive Trading Chart Visualization */}
      <div className="absolute top-1/4 right-10 w-80 h-48 opacity-20 hidden lg:block">
        <div className="relative w-full h-full">
          {/* Animated Chart Lines */}
          <svg className="w-full h-full" viewBox="0 0 320 192">
            <defs>
              <linearGradient id="chartGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#3B82F6" stopOpacity="0.8" />
                <stop offset="50%" stopColor="#8B5CF6" stopOpacity="0.6" />
                <stop offset="100%" stopColor="#06B6D4" stopOpacity="0.4" />
              </linearGradient>
            </defs>
            <path
              d="M0,150 Q80,120 160,100 T320,80"
              stroke="url(#chartGradient)"
              strokeWidth="3"
              fill="none"
              className="animate-chart-draw"
            />
            <path
              d="M0,160 Q80,140 160,130 T320,110"
              stroke="#10B981"
              strokeWidth="2"
              fill="none"
              opacity="0.6"
              className="animate-chart-draw delay-500"
            />
          </svg>

          {/* Floating Data Points */}
          <div className="absolute top-8 left-16 w-2 h-2 bg-green-400 rounded-full animate-pulse" />
          <div className="absolute top-12 right-20 w-1.5 h-1.5 bg-blue-400 rounded-full animate-pulse delay-300" />
          <div className="absolute bottom-16 left-1/3 w-1 h-1 bg-purple-400 rounded-full animate-pulse delay-600" />
        </div>
      </div>

      {/* Main Content */}
      <Container>
        <div className="text-center pt-32 pb-16">
          {/* Animated Status Badge */}
          <div className="mb-8 animate-fade-in-up">
            <div className="inline-flex items-center gap-3 bg-gradient-to-r from-gray-900/90 to-black/90 backdrop-blur-xl border border-gray-800/50 rounded-full px-6 py-3 mb-8 shadow-2xl hover:scale-105 transition-transform duration-300 cursor-pointer">
              <div className="relative">
                <Zap className="w-5 h-5 text-blue-400 animate-pulse" />
                <div className="absolute inset-0 w-5 h-5 bg-blue-400/20 rounded-full animate-ping" />
              </div>
              <span className="text-gray-300 text-sm font-medium tracking-wide">
                AI-POWERED TRADING REVOLUTION
              </span>
              <div className="flex gap-1">
                <div className="w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse" />
                <div className="w-1.5 h-1.5 bg-blue-400 rounded-full animate-pulse delay-200" />
                <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-pulse delay-400" />
              </div>
            </div>
          </div>

          {/* Dynamic Typography with Glitch Effect */}
          <div className="mb-8">
            <h1 className="text-6xl md:text-8xl font-black text-white mb-4 leading-none animate-fade-in-up delay-200 tracking-tight relative">
              <span className="relative inline-block group">
                <span className="bg-gradient-to-r from-white via-gray-100 to-white bg-clip-text text-transparent group-hover:animate-glitch">
                  AI-POWERED
                </span>
                <div className="absolute -inset-2 bg-gradient-to-r from-blue-600/20 to-purple-600/20 blur-xl opacity-50 animate-pulse group-hover:opacity-80 transition-opacity duration-300" />
              </span>
            </h1>

            <h2 className="text-5xl md:text-7xl font-black mb-6 animate-fade-in-up delay-300 tracking-tight relative group cursor-pointer">
              <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent animate-gradient bg-300 leading-none group-hover:scale-105 transition-transform duration-300 inline-block">
                CRYPTO TRADING
              </span>
              <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-0 h-1 bg-gradient-to-r from-blue-500 to-purple-500 group-hover:w-full transition-all duration-500" />
            </h2>

            <div className="relative inline-block animate-fade-in-up delay-400 group cursor-pointer">
              <h3 className="text-4xl md:text-6xl font-black text-white tracking-tight group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-cyan-400 group-hover:to-blue-400 group-hover:bg-clip-text transition-all duration-500">
                BOTS
              </h3>
              <div className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-cyan-500 rounded-full animate-pulse" />

              {/* Floating Icons */}
              <div className="absolute -top-4 -right-8 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center animate-bounce">
                  <TrendingUp className="w-3 h-3 text-blue-400" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </Container>

      {/* Bottom Gradient Fade */}
      <div className="absolute bottom-0 left-0 right-0 h-40 bg-gradient-to-t from-gray-900 via-gray-900/50 to-transparent" />
    </Section>
  );
};

export default Hero;