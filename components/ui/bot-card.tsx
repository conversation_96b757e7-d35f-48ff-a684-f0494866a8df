import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { LucideIcon, ArrowRight } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "./button"

const botCardVariants = cva(
  "group bg-black rounded-3xl p-8 hover:bg-gray-900/50 transition-all duration-500 transform hover:-translate-y-2 animate-scale-in",
  {
    variants: {
      colorScheme: {
        blue: "border-2 border-blue-500/30",
        purple: "border-2 border-purple-500/30",
        amber: "border-2 border-amber-500/30",
        green: "border-2 border-green-500/30",
        cyan: "border-2 border-cyan-500/30",
        pink: "border-2 border-pink-500/30",
      },
    },
    defaultVariants: {
      colorScheme: "blue",
    },
  }
)

const botIconVariants = cva(
  "rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300",
  {
    variants: {
      colorScheme: {
        blue: "w-16 h-16 bg-blue-500/10 border border-blue-500/30",
        purple: "w-16 h-16 bg-purple-500/10 border border-purple-500/30",
        amber: "w-16 h-16 bg-amber-500/10 border border-amber-500/30",
        green: "w-16 h-16 bg-green-500/10 border border-green-500/30",
        cyan: "w-16 h-16 bg-cyan-500/10 border border-cyan-500/30",
        pink: "w-16 h-16 bg-pink-500/10 border border-pink-500/30",
      },
    },
    defaultVariants: {
      colorScheme: "blue",
    },
  }
)

const botROIVariants = cva(
  "text-3xl font-bold bg-gradient-to-r bg-clip-text text-transparent",
  {
    variants: {
      colorScheme: {
        blue: "from-blue-500 to-cyan-500",
        purple: "from-purple-500 to-pink-500",
        amber: "from-amber-500 to-orange-500",
        green: "from-green-500 to-emerald-500",
        cyan: "from-cyan-500 to-blue-500",
        pink: "from-pink-500 to-rose-500",
      },
    },
    defaultVariants: {
      colorScheme: "blue",
    },
  }
)

const botButtonVariants = cva(
  "w-full text-white py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 group-hover:scale-105 flex items-center justify-center gap-2 bg-gradient-to-r",
  {
    variants: {
      colorScheme: {
        blue: "from-blue-500 to-cyan-500",
        purple: "from-purple-500 to-pink-500",
        amber: "from-amber-500 to-orange-500",
        green: "from-green-500 to-emerald-500",
        cyan: "from-cyan-500 to-blue-500",
        pink: "from-pink-500 to-rose-500",
      },
    },
    defaultVariants: {
      colorScheme: "blue",
    },
  }
)

const botIconColorMap = {
  blue: "text-blue-400",
  purple: "text-purple-400",
  amber: "text-amber-400",
  green: "text-green-400",
  cyan: "text-cyan-400",
  pink: "text-pink-400",
} as const

export interface BotCardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof botCardVariants> {
  icon: LucideIcon
  name: string
  roi: string
  period: string
  strategy: string
  buttonText: string
  delay?: number
  onButtonClick?: () => void
}

const BotCard = React.forwardRef<HTMLDivElement, BotCardProps>(
  ({ 
    className, 
    colorScheme = "blue", 
    icon: Icon, 
    name, 
    roi, 
    period, 
    strategy, 
    buttonText,
    delay = 0,
    onButtonClick,
    ...props 
  }, ref) => (
    <div
      ref={ref}
      className={cn(botCardVariants({ colorScheme, className }))}
      style={{ animationDelay: `${delay}ms` }}
      {...props}
    >
      <div className={cn(botIconVariants({ colorScheme }))}>
        <Icon className={cn("w-8 h-8", botIconColorMap[colorScheme])} />
      </div>

      <h3 className="text-2xl font-bold text-white mb-2">{name}</h3>

      <div className="mb-4">
        <span className={cn(botROIVariants({ colorScheme }))}>
          {roi}
        </span>
        <span className="text-gray-400 ml-2">ROI in last {period}</span>
      </div>

      <p className="text-gray-400 mb-6">
        <span className="font-medium text-gray-300">Strategy:</span> {strategy}
      </p>

      <button
        className={cn(botButtonVariants({ colorScheme }))}
        onClick={onButtonClick}
        type="button"
      >
        {buttonText}
        <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
      </button>
    </div>
  )
)
BotCard.displayName = "BotCard"

export { BotCard }
