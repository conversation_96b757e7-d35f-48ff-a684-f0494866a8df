import { cva, type VariantProps } from "class-variance-authority";
import type { LucideIcon } from "lucide-react";
import * as React from "react";

import { cn } from "@/lib/utils";

const featureCardVariants = cva(
  "group relative rounded-3xl border transition-all duration-500 transform",
  {
    variants: {
      variant: {
        default: "bg-black border-gray-800/50 hover:bg-gray-800/50 hover:border-gray-700/70",
        secondary:
          "bg-gray-900/50 border-gray-800/50 hover:bg-gray-800/50 hover:border-gray-700/70",
        gradient: "bg-gradient-to-br backdrop-blur-xl border-gray-800/50 hover:border-gray-700/70",
      },
      colorScheme: {
        blue: "border-blue-500/30",
        purple: "border-purple-500/30",
        amber: "border-amber-500/30",
        green: "border-green-500/30",
        cyan: "border-cyan-500/30",
        pink: "border-pink-500/30",
      },
      size: {
        default: "p-8",
        sm: "p-6",
        lg: "p-10",
      },
      hover: {
        lift: "hover:-translate-y-2",
        float: "hover:-translate-y-4",
        scale: "hover:scale-105",
        rotate: "hover:rotate-1",
      },
      animation: {
        none: "",
        "scale-in": "animate-scale-in",
        "fade-in-up": "animate-fade-in-up",
        "slide-in-left": "animate-slide-in-left",
      },
    },
    defaultVariants: {
      variant: "default",
      colorScheme: "blue",
      size: "default",
      hover: "lift",
      animation: "scale-in",
    },
  }
);

const featureIconVariants = cva(
  "rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300",
  {
    variants: {
      size: {
        default: "w-16 h-16",
        sm: "w-12 h-12",
        lg: "w-20 h-20",
      },
      colorScheme: {
        blue: "bg-blue-500/10 border border-blue-500/30",
        purple: "bg-purple-500/10 border border-purple-500/30",
        amber: "bg-amber-500/10 border border-amber-500/30",
        green: "bg-green-500/10 border border-green-500/30",
        cyan: "bg-cyan-500/10 border border-cyan-500/30",
        pink: "bg-pink-500/10 border border-pink-500/30",
      },
    },
    defaultVariants: {
      size: "default",
      colorScheme: "blue",
    },
  }
);

const featureIconColorMap = {
  blue: "text-blue-400",
  purple: "text-purple-400",
  amber: "text-amber-400",
  green: "text-green-400",
  cyan: "text-cyan-400",
  pink: "text-pink-400",
} as const;

export interface FeatureCardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof featureCardVariants> {
  icon: LucideIcon;
  title: string;
  description: string;
  delay?: number;
}

const FeatureCard = React.forwardRef<HTMLDivElement, FeatureCardProps>(
  (
    {
      className,
      variant,
      colorScheme = "blue",
      size,
      hover,
      animation,
      icon: Icon,
      title,
      description,
      delay = 0,
      ...props
    },
    ref
  ) => (
    <div
      ref={ref}
      className={cn(
        featureCardVariants({ variant, colorScheme, size, hover, animation, className })
      )}
      style={{ animationDelay: `${delay}ms` }}
      {...props}
    >
      <div className={cn(featureIconVariants({ size, colorScheme }))}>
        <Icon className={cn("w-8 h-8", featureIconColorMap[colorScheme])} />
      </div>
      <h3 className="text-2xl font-bold text-white mb-4">{title}</h3>
      <p className="text-gray-400 leading-relaxed">{description}</p>
    </div>
  )
);
FeatureCard.displayName = "FeatureCard";

export { FeatureCard };
