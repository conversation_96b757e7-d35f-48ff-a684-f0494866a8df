import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const pageHeaderVariants = cva(
  "text-center mb-16",
  {
    variants: {
      size: {
        default: "mb-16",
        sm: "mb-12",
        lg: "mb-20",
      },
      animation: {
        none: "",
        "fade-in": "animate-fade-in-up",
      },
    },
    defaultVariants: {
      size: "default",
      animation: "fade-in",
    },
  }
)

const pageHeaderTitleVariants = cva(
  "font-bold text-white mb-4 leading-tight",
  {
    variants: {
      size: {
        default: "text-4xl md:text-5xl",
        sm: "text-3xl md:text-4xl",
        lg: "text-5xl md:text-6xl",
        xl: "text-6xl md:text-7xl",
      },
      gradient: {
        none: "",
        blue: "bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent",
        purple: "bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent",
        amber: "bg-gradient-to-r from-amber-400 to-orange-400 bg-clip-text text-transparent",
        cyan: "bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent",
      },
    },
    defaultVariants: {
      size: "default",
      gradient: "none",
    },
  }
)

const pageHeaderDescriptionVariants = cva(
  "text-gray-400 mx-auto leading-relaxed",
  {
    variants: {
      size: {
        default: "text-xl max-w-3xl",
        sm: "text-lg max-w-2xl",
        lg: "text-2xl max-w-4xl",
      },
      animation: {
        none: "",
        "fade-in": "animate-fade-in-up delay-200",
      },
    },
    defaultVariants: {
      size: "default",
      animation: "fade-in",
    },
  }
)

export interface PageHeaderProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof pageHeaderVariants> {}

export interface PageHeaderTitleProps
  extends React.HTMLAttributes<HTMLHeadingElement>,
    VariantProps<typeof pageHeaderTitleVariants> {}

export interface PageHeaderDescriptionProps
  extends React.HTMLAttributes<HTMLParagraphElement>,
    VariantProps<typeof pageHeaderDescriptionVariants> {}

const PageHeader = React.forwardRef<HTMLDivElement, PageHeaderProps>(
  ({ className, size, animation, ...props }, ref) => (
    <div ref={ref} className={cn(pageHeaderVariants({ size, animation, className }))} {...props} />
  )
)
PageHeader.displayName = "PageHeader"

const PageHeaderTitle = React.forwardRef<HTMLHeadingElement, PageHeaderTitleProps>(
  ({ className, size, gradient, ...props }, ref) => (
    <h1 ref={ref} className={cn(pageHeaderTitleVariants({ size, gradient, className }))} {...props} />
  )
)
PageHeaderTitle.displayName = "PageHeaderTitle"

const PageHeaderDescription = React.forwardRef<HTMLParagraphElement, PageHeaderDescriptionProps>(
  ({ className, size, animation, ...props }, ref) => (
    <p ref={ref} className={cn(pageHeaderDescriptionVariants({ size, animation, className }))} {...props} />
  )
)
PageHeaderDescription.displayName = "PageHeaderDescription"

export { PageHeader, PageHeaderTitle, PageHeaderDescription }
