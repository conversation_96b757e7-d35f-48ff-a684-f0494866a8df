import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const sectionVariants = cva(
  "relative",
  {
    variants: {
      variant: {
        default: "py-20",
        hero: "min-h-screen pt-32 pb-16",
        compact: "py-16",
        large: "py-24",
      },
      background: {
        black: "bg-black",
        gray: "bg-gray-900",
        transparent: "bg-transparent",
        gradient: "bg-gradient-to-br from-black via-gray-900 to-black",
        "gradient-blue": "bg-gradient-to-r from-blue-600 to-purple-600",
      },
      overflow: {
        visible: "",
        hidden: "overflow-hidden",
      },
    },
    defaultVariants: {
      variant: "default",
      background: "black",
      overflow: "visible",
    },
  }
)

const containerVariants = cva(
  "mx-auto px-6",
  {
    variants: {
      size: {
        default: "max-w-7xl",
        sm: "max-w-4xl",
        lg: "max-w-8xl",
        full: "max-w-full",
      },
    },
    defaultVariants: {
      size: "default",
    },
  }
)

export interface SectionProps
  extends React.HTMLAttributes<HTMLElement>,
    VariantProps<typeof sectionVariants> {}

export interface ContainerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof containerVariants> {}

const Section = React.forwardRef<HTMLElement, SectionProps>(
  ({ className, variant, background, overflow, ...props }, ref) => (
    <section
      ref={ref}
      className={cn(sectionVariants({ variant, background, overflow, className }))}
      {...props}
    />
  )
)
Section.displayName = "Section"

const Container = React.forwardRef<HTMLDivElement, ContainerProps>(
  ({ className, size, ...props }, ref) => (
    <div ref={ref} className={cn(containerVariants({ size, className }))} {...props} />
  )
)
Container.displayName = "Container"

export { Section, Container }
