import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { LucideIcon } from "lucide-react"

import { cn } from "@/lib/utils"

const statsCardVariants = cva(
  "group text-center rounded-3xl border transition-all duration-500 animate-scale-in",
  {
    variants: {
      variant: {
        default: "bg-gray-900/50 border-gray-800/50 hover:bg-gray-800/50",
        solid: "bg-black border-gray-800/50 hover:bg-gray-800/50",
      },
      colorScheme: {
        blue: "border-blue-500/30",
        purple: "border-purple-500/30",
        amber: "border-amber-500/30",
        green: "border-green-500/30",
        cyan: "border-cyan-500/30",
        pink: "border-pink-500/30",
      },
      size: {
        default: "p-8",
        sm: "p-6",
        lg: "p-10",
      },
    },
    defaultVariants: {
      variant: "default",
      colorScheme: "blue",
      size: "default",
    },
  }
)

const statsIconVariants = cva(
  "rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300",
  {
    variants: {
      size: {
        default: "w-16 h-16",
        sm: "w-12 h-12",
        lg: "w-20 h-20",
      },
      colorScheme: {
        blue: "bg-blue-500/10 border border-blue-500/30",
        purple: "bg-purple-500/10 border border-purple-500/30",
        amber: "bg-amber-500/10 border border-amber-500/30",
        green: "bg-green-500/10 border border-green-500/30",
        cyan: "bg-cyan-500/10 border border-cyan-500/30",
        pink: "bg-pink-500/10 border border-pink-500/30",
      },
    },
    defaultVariants: {
      size: "default",
      colorScheme: "blue",
    },
  }
)

const statsValueVariants = cva(
  "font-bold mb-2 group-hover:scale-110 transition-transform duration-300",
  {
    variants: {
      size: {
        default: "text-4xl",
        sm: "text-3xl",
        lg: "text-5xl",
      },
      colorScheme: {
        blue: "text-blue-400",
        purple: "text-purple-400",
        amber: "text-amber-400",
        green: "text-green-400",
        cyan: "text-cyan-400",
        pink: "text-pink-400",
      },
    },
    defaultVariants: {
      size: "default",
      colorScheme: "blue",
    },
  }
)

const statsIconColorMap = {
  blue: "text-blue-400",
  purple: "text-purple-400",
  amber: "text-amber-400",
  green: "text-green-400",
  cyan: "text-cyan-400",
  pink: "text-pink-400",
} as const

export interface StatsCardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof statsCardVariants> {
  icon: LucideIcon
  value: string
  label: string
  delay?: number
}

const StatsCard = React.forwardRef<HTMLDivElement, StatsCardProps>(
  ({ 
    className, 
    variant, 
    colorScheme = "blue", 
    size, 
    icon: Icon, 
    value, 
    label, 
    delay = 0,
    ...props 
  }, ref) => (
    <div
      ref={ref}
      className={cn(statsCardVariants({ variant, colorScheme, size, className }))}
      style={{ animationDelay: `${delay}ms` }}
      {...props}
    >
      <div className={cn(statsIconVariants({ size, colorScheme }))}>
        <Icon className={cn("w-8 h-8", statsIconColorMap[colorScheme])} />
      </div>
      <div className={cn(statsValueVariants({ size, colorScheme }))}>
        {value}
      </div>
      <p className="text-gray-400 text-sm leading-relaxed">{label}</p>
    </div>
  )
)
StatsCard.displayName = "StatsCard"

export { StatsCard }
