{"name": "lightquant", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "biome lint .", "format": "biome format --write .", "db:generate": "prisma generate", "db:migrate": "prisma migrate deploy"}, "dependencies": {"@prisma/client": "^6.11.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "gray-matter": "^4.0.3", "lucide-react": "^0.517.0", "next": "^15.3.4", "next-auth": "^4.24.11", "react": "^19.1.0", "react-dom": "^19.1.0", "react-fast-marquee": "^1.6.5", "remark": "^15.0.1", "remark-html": "^16.0.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@biomejs/biome": "^2.0.5", "@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.18", "postcss": "^8.4.35", "prisma": "^6.11.1", "tailwindcss": "^3.4.1", "typescript": "^5.5.3"}}